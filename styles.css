/* ===================================================================
   CREATIVE HYDROLICS - PREMIUM HYDRAULIC SOLUTIONS
   Royal-Grade Styling System
   ================================================================= */

/* ===================================================================
   CSS CUSTOM PROPERTIES (CSS VARIABLES)
   ================================================================= */
:root {
    /* Primary Color Palette */
    --royal-blue: #0A2463;
    --royal-blue-dark: #081d52;
    --royal-blue-light: #1a365d;
    --crimson: #D7263D;
    --crimson-dark: #b91c2c;
    --crimson-light: #ff4d6d;
    --gold: #DAA520;
    --gold-dark: #b8941c;
    --gold-light: #ffd700;
    --light-blue: #B6D6F2;
    --light-blue-dark: #93c5ed;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Gradient Definitions */
    --gradient-royal: linear-gradient(135deg, var(--royal-blue), var(--royal-blue-light));
    --gradient-crimson: linear-gradient(135deg, var(--crimson), var(--crimson-light));
    --gradient-gold: linear-gradient(135deg, var(--gold), var(--gold-light));
    --gradient-hero: linear-gradient(135deg, var(--royal-blue) 0%, var(--royal-blue-light) 50%, var(--gray-800) 100%);
    --gradient-text: linear-gradient(135deg, var(--crimson), var(--crimson-light), var(--crimson));

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-royal: 0 10px 25px rgba(10, 36, 99, 0.15);
    --shadow-crimson: 0 10px 25px rgba(215, 38, 61, 0.15);
    --shadow-gold: 0 10px 25px rgba(218, 165, 32, 0.15);
    --shadow-glow: 0 0 20px rgba(215, 38, 61, 0.3);

    /* Typography */
    --font-montserrat: 'Montserrat', sans-serif;
    --font-playfair: 'Playfair Display', serif;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    --transition-bounce: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===================================================================
   GLOBAL STYLES & RESETS
   ================================================================= */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--font-montserrat);
    line-height: 1.6;
    color: var(--gray-700);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-200) 100%);
    overflow-x: hidden;
}

/* Custom Selection */
::selection {
    background: var(--crimson);
    color: var(--white);
}

::-moz-selection {
    background: var(--crimson);
    color: var(--white);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-royal);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-crimson);
}

/* ===================================================================
   UTILITY CLASSES
   ================================================================= */
.text-gradient {
    background: var(--gradient-text);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientText 8s linear infinite;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
    transition: transform var(--transition-bounce);
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.hover-scale {
    transition: transform var(--transition-bounce);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ===================================================================
   HEADER & NAVIGATION
   ================================================================= */
header {
    backdrop-filter: blur(15px);
    background: rgba(10, 36, 99, 0.95) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
    z-index: 1000;
}

header.scrolled {
    background: rgba(10, 36, 99, 0.98) !important;
    box-shadow: var(--shadow-lg);
}

/* Logo Animation */
.logo-container {
    transition: transform var(--transition-normal);
}

.logo-container:hover {
    transform: scale(1.05);
}

.logo-container .w-12 {
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.logo-container .w-12::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.logo-container:hover .w-12::before {
    left: 100%;
}

/* Navigation Links */
header nav a {
    position: relative;
    padding: var(--space-sm) 0;
    overflow: hidden;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all var(--transition-normal);
}

header nav a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-crimson);
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
}

header nav a::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(215, 38, 61, 0.1);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform var(--transition-normal);
    z-index: -1;
    border-radius: var(--radius-md);
}

header nav a:hover::before {
    transform: translateX(0);
}

header nav a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

header nav a:hover {
    color: var(--white);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Mobile Menu Button */
.mobile-menu-button {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
}

.mobile-menu-button:hover {
    background: rgba(215, 38, 61, 0.2);
    transform: scale(1.1);
}

.mobile-menu-button i {
    transition: transform var(--transition-normal);
}

.mobile-menu-button:hover i {
    transform: rotate(90deg);
}

/* ===================================================================
   ULTRA-PREMIUM HERO SECTION
   ================================================================= */
.hero-section {
    position: relative;
    min-height: 100vh;
    background: var(--gradient-hero);
    background-size: 400% 400%;
    animation: gradientFlow 15s ease infinite;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Particle System */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particleFloat 20s infinite linear;
}

.particle-1 { top: 20%; left: 10%; animation-delay: 0s; }
.particle-2 { top: 40%; left: 80%; animation-delay: -5s; }
.particle-3 { top: 60%; left: 20%; animation-delay: -10s; }
.particle-4 { top: 80%; left: 70%; animation-delay: -15s; }
.particle-5 { top: 30%; left: 50%; animation-delay: -8s; }
.particle-6 { top: 70%; left: 90%; animation-delay: -12s; }

/* Geometric Pattern Overlay */
.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    opacity: 0.03;
}

.pattern-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: patternMove 30s linear infinite;
}

.pattern-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.05) 50%, transparent 60%);
    background-size: 100px 100px;
    animation: patternMove 25s linear infinite reverse;
}

/* Hero Background Decoration */
.hero-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(215, 38, 61, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(10, 36, 99, 0.1) 0%, transparent 50%);
    z-index: 1;
}

/* Animated Background Shapes */
.hero-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    opacity: 0.08;
    animation: floatShape 25s infinite ease-in-out;
}

.shape-1 {
    width: 500px;
    height: 500px;
    background: var(--gradient-crimson);
    top: -150px;
    right: -150px;
    animation-delay: 0s;
}

.shape-2 {
    width: 400px;
    height: 400px;
    background: var(--gradient-royal);
    bottom: -100px;
    left: -100px;
    animation-delay: -8s;
}

.shape-3 {
    width: 300px;
    height: 300px;
    background: var(--gradient-gold);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: -16s;
}

.shape-4 {
    width: 250px;
    height: 250px;
    background: var(--gradient-crimson);
    top: 20%;
    left: 10%;
    animation-delay: -4s;
}

.shape-5 {
    width: 350px;
    height: 350px;
    background: var(--gradient-royal);
    bottom: 20%;
    right: 15%;
    animation-delay: -12s;
}

/* Enhanced Hero Badge */
.hero-badge-enhanced {
    position: relative;
    display: inline-flex;
    align-items: center;
    margin-bottom: var(--space-3xl);
    animation: fadeInDown 0.8s ease-out;
}

.badge-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, var(--crimson), var(--gold), var(--royal-blue), var(--crimson));
    background-size: 400% 400%;
    border-radius: var(--radius-full);
    filter: blur(15px);
    opacity: 0.3;
    animation: gradientFlow 8s ease infinite;
    z-index: -1;
}

.badge-content {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-full);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.badge-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 1.5s ease;
}

.hero-badge-enhanced:hover .badge-content::before {
    left: 100%;
}

.hero-badge-enhanced:hover .badge-content {
    transform: translateY(-3px);
    border-color: var(--gold);
    box-shadow: 0 10px 30px rgba(218, 165, 32, 0.3);
}

.badge-icon {
    color: var(--gold);
    margin-right: var(--space-md);
    font-size: 1.4rem;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 0 10px var(--gold));
}

.badge-text {
    font-size: 1rem;
    font-weight: 700;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: var(--white);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.badge-sparkle {
    position: absolute;
    top: -5px;
    right: -5px;
    display: flex;
    gap: 3px;
}

.badge-sparkle i {
    font-size: 0.6rem;
    color: var(--gold);
    animation: sparkle 2s ease-in-out infinite;
}

.sparkle-1 { animation-delay: 0s; }
.sparkle-2 { animation-delay: 0.7s; }
.sparkle-3 { animation-delay: 1.4s; }

/* Enhanced Hero Title */
.hero-title-container {
    position: relative;
    margin-bottom: var(--space-3xl);
    animation: fadeInUp 1s ease-out 0.3s forwards;
    opacity: 0;
}

.hero-main-title {
    font-family: var(--font-playfair);
    font-weight: 800;
    line-height: 1.1;
    text-align: center;
    margin-bottom: var(--space-xl);
    position: relative;
}

.title-line-1 {
    display: block;
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: var(--white);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: slideInLeft 1s ease-out 0.5s forwards;
    opacity: 0;
    transform: translateX(-50px);
}

.title-line-2 {
    display: block;
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    color: var(--light-blue);
    margin: var(--space-sm) 0;
    font-weight: 400;
    animation: fadeIn 1s ease-out 0.8s forwards;
    opacity: 0;
}

.title-line-3 {
    display: block;
    font-size: clamp(3rem, 6vw, 5.5rem);
    font-weight: 900;
    animation: slideInRight 1s ease-out 1.1s forwards;
    opacity: 0;
    transform: translateX(50px);
}

.text-gradient-enhanced {
    background: linear-gradient(135deg, var(--crimson) 0%, var(--gold) 25%, var(--crimson-light) 50%, var(--gold) 75%, var(--crimson) 100%);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientText 6s ease infinite;
    filter: drop-shadow(0 4px 8px rgba(215, 38, 61, 0.3));
}

.title-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-lg);
    margin-top: var(--space-lg);
    animation: fadeIn 1s ease-out 1.4s forwards;
    opacity: 0;
}

.decoration-line {
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--gold), transparent);
    position: relative;
}

.decoration-line::after {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(218, 165, 32, 0.3), transparent);
    filter: blur(2px);
}

.decoration-diamond {
    width: 40px;
    height: 40px;
    background: var(--gradient-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    animation: rotate 10s linear infinite;
    box-shadow: 0 0 20px rgba(218, 165, 32, 0.4);
}

.decoration-diamond i {
    color: var(--white);
    font-size: 1.2rem;
    animation: float 3s ease-in-out infinite;
}

/* Enhanced Hero Subtitle */
.hero-subtitle-container {
    position: relative;
    max-width: 900px;
    margin: 0 auto var(--space-3xl);
    animation: fadeInUp 1s ease-out 1.7s forwards;
    opacity: 0;
}

.hero-subtitle-enhanced {
    font-size: clamp(1.2rem, 2.5vw, 1.6rem);
    line-height: 1.8;
    color: var(--light-blue);
    font-weight: 400;
    text-align: center;
    margin-bottom: var(--space-lg);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.subtitle-highlight {
    color: var(--white);
    font-weight: 600;
    position: relative;
    padding: 0 4px;
}

.subtitle-highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--crimson), var(--gold));
    opacity: 0.6;
    border-radius: 1px;
}

.subtitle-underline {
    width: 120px;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--crimson), var(--gold), var(--crimson), transparent);
    margin: 0 auto;
    border-radius: 2px;
    animation: expandWidth 2s ease-out 2s forwards;
    transform: scaleX(0);
}

/* Enhanced Hero Statistics */
.hero-stats-enhanced {
    margin-bottom: var(--space-3xl);
    animation: fadeInUp 1s ease-out 2.2s forwards;
    opacity: 0;
}

.stats-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-lg);
    flex-wrap: wrap;
    max-width: 1000px;
    margin: 0 auto;
}

.stat-item-enhanced {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--space-xl);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-bounce);
    min-width: 180px;
    overflow: hidden;
}

.stat-item-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 1s ease;
}

.stat-item-enhanced:hover::before {
    left: 100%;
}

.stat-item-enhanced:hover {
    transform: translateY(-8px) scale(1.05);
    border-color: var(--gold);
    box-shadow: 0 15px 40px rgba(218, 165, 32, 0.2);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-crimson);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-md);
    animation: float 4s ease-in-out infinite;
    box-shadow: 0 5px 15px rgba(215, 38, 61, 0.3);
}

.stat-icon i {
    color: var(--white);
    font-size: 1.3rem;
}

.stat-content {
    position: relative;
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: var(--space-sm);
}

.stat-number {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 900;
    color: var(--white);
    line-height: 1;
    font-family: var(--font-playfair);
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    animation: countUp 2s ease-out 2.5s forwards;
}

.stat-plus, .stat-percent {
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    font-weight: 700;
    color: var(--crimson);
    margin-left: 4px;
    animation: fadeIn 1s ease-out 4s forwards;
    opacity: 0;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--light-blue);
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 600;
    text-align: center;
}

.stat-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, var(--crimson), var(--gold), var(--royal-blue));
    background-size: 400% 400%;
    border-radius: var(--radius-2xl);
    filter: blur(20px);
    opacity: 0;
    animation: gradientFlow 8s ease infinite;
    z-index: -1;
    transition: opacity var(--transition-normal);
}

.stat-item-enhanced:hover .stat-glow {
    opacity: 0.3;
}

.stat-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100px;
    justify-content: center;
}

.divider-line {
    width: 2px;
    height: 60px;
    background: linear-gradient(to bottom, transparent, var(--gold), transparent);
    margin-bottom: var(--space-sm);
}

.divider-dot {
    width: 8px;
    height: 8px;
    background: var(--gold);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--gold);
    animation: pulse 2s ease-in-out infinite;
}

/* Premium Enhanced Buttons */
.hero-buttons-enhanced {
    display: flex;
    justify-content: center;
    gap: var(--space-xl);
    margin-bottom: var(--space-3xl);
    animation: fadeInUp 1s ease-out 2.7s forwards;
    opacity: 0;
    flex-wrap: wrap;
}

.hero-button-premium {
    position: relative;
    padding: var(--space-lg) var(--space-2xl);
    font-size: 1.1rem;
    font-weight: 700;
    border-radius: var(--radius-full);
    overflow: hidden;
    transition: all var(--transition-bounce);
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 220px;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    backdrop-filter: blur(20px);
}

.hero-button-premium.primary {
    background: var(--gradient-crimson);
    color: var(--white);
    border: 2px solid var(--crimson-light);
    box-shadow: 0 10px 30px rgba(215, 38, 61, 0.3);
}

.hero-button-premium.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.button-bg-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%) skewX(-15deg);
    transition: transform 0.8s ease;
    z-index: 1;
}

.hero-button-premium:hover .button-bg-effect {
    transform: translateX(100%) skewX(-15deg);
}

.button-border-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--crimson), var(--gold), var(--royal-blue), var(--crimson));
    background-size: 400% 400%;
    border-radius: var(--radius-full);
    opacity: 0;
    animation: gradientFlow 8s ease infinite;
    z-index: -1;
    transition: opacity var(--transition-normal);
}

.hero-button-premium:hover .button-border-glow {
    opacity: 0.8;
}

.hero-button-premium:hover {
    transform: translateY(-5px) scale(1.05);
}

.hero-button-premium.primary:hover {
    box-shadow: 0 20px 50px rgba(215, 38, 61, 0.4);
    border-color: var(--gold);
}

.hero-button-premium.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--gold);
    box-shadow: 0 15px 40px rgba(218, 165, 32, 0.3);
}

.button-content-enhanced {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.button-icon-left {
    transition: transform var(--transition-normal);
    font-size: 1rem;
}

.button-text-main {
    font-weight: 700;
    font-size: 1rem;
}

.button-icon-right {
    transition: transform var(--transition-normal);
    font-size: 0.9rem;
}

.hero-button-premium:hover .button-icon-left {
    transform: translateX(-3px);
}

.hero-button-premium:hover .button-icon-right {
    transform: translateX(5px);
}

.button-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
    z-index: 1;
}

.hero-button-premium:active .button-ripple {
    width: 300px;
    height: 300px;
}

/* Enhanced Trust Indicators */
.hero-trust-enhanced {
    animation: fadeInUp 1s ease-out 3.2s forwards;
    opacity: 0;
    text-align: center;
}

.trust-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.trust-line {
    width: 100px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.trust-label-enhanced {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    padding: var(--space-sm) var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-full);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.trust-label-enhanced i {
    color: var(--gold);
    font-size: 1rem;
}

.trust-logos-enhanced {
    display: flex;
    justify-content: center;
    gap: var(--space-lg);
    flex-wrap: wrap;
    max-width: 800px;
    margin: 0 auto;
}

.trust-logo-enhanced {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    padding: var(--space-md) var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-bounce);
    font-weight: 500;
    min-width: 140px;
    justify-content: center;
    overflow: hidden;
}

.trust-logo-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.trust-logo-enhanced:hover::before {
    left: 100%;
}

.trust-logo-enhanced:hover {
    transform: translateY(-3px) scale(1.05);
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-color: var(--gold);
    box-shadow: 0 10px 25px rgba(218, 165, 32, 0.2);
}

.logo-glow {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, var(--crimson), var(--gold), var(--royal-blue));
    background-size: 400% 400%;
    border-radius: var(--radius-xl);
    filter: blur(10px);
    opacity: 0;
    animation: gradientFlow 8s ease infinite;
    z-index: -1;
    transition: opacity var(--transition-normal);
}

.trust-logo-enhanced:hover .logo-glow {
    opacity: 0.4;
}

.logo-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.logo-content i {
    color: var(--gold);
    font-size: 1.1rem;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 0 5px var(--gold));
}

/* Enhanced Bottom Gradient */
.hero-bottom-gradient {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 200px;
    z-index: 5;
}

.gradient-mesh {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 100%, rgba(215, 38, 61, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 100%, rgba(218, 165, 32, 0.1) 0%, transparent 50%);
}

.gradient-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
}

/* Premium Scroll Indicator */
.scroll-indicator-enhanced {
    position: absolute;
    bottom: var(--space-2xl);
    left: 50%;
    transform: translateX(-50%);
    z-index: 20;
    animation: fadeIn 1s ease-out 3.5s forwards;
    opacity: 0;
    cursor: pointer;
    transition: all var(--transition-bounce);
    text-align: center;
}

.scroll-indicator-enhanced:hover {
    transform: translateX(-50%) scale(1.1);
}

.scroll-text {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: var(--space-sm);
    font-weight: 600;
    transition: color var(--transition-normal);
}

.scroll-indicator-enhanced:hover .scroll-text {
    color: var(--gold);
}

.scroll-mouse-enhanced {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
}

.mouse-body {
    width: 36px;
    height: 60px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 30px;
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    transition: all var(--transition-normal);
}

.scroll-indicator-enhanced:hover .mouse-body {
    border-color: var(--gold);
    background: rgba(218, 165, 32, 0.1);
    box-shadow: 0 0 20px rgba(218, 165, 32, 0.3);
}

.mouse-wheel {
    width: 4px;
    height: 12px;
    background: var(--white);
    border-radius: 2px;
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    animation: scroll 2.5s infinite ease-in-out;
    transition: background var(--transition-normal);
}

.scroll-indicator-enhanced:hover .mouse-wheel {
    background: var(--gold);
}

.scroll-arrows {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.arrow-line {
    width: 12px;
    height: 12px;
    border-right: 2px solid rgba(255, 255, 255, 0.6);
    border-bottom: 2px solid rgba(255, 255, 255, 0.6);
    transform: rotate(45deg);
    animation: arrowDown 2.5s infinite ease-in-out;
    opacity: 0;
    transition: border-color var(--transition-normal);
}

.scroll-indicator-enhanced:hover .arrow-line {
    border-color: var(--gold);
}

.arrow-line:nth-child(1) { animation-delay: 0s; }
.arrow-line:nth-child(2) { animation-delay: 0.3s; }
.arrow-line:nth-child(3) { animation-delay: 0.6s; }

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 4;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: floatIcon 15s infinite ease-in-out;
}

.floating-icon i {
    color: var(--gold);
    font-size: 1.5rem;
    opacity: 0.6;
    animation: float 4s ease-in-out infinite;
}

.floating-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-2 {
    top: 60%;
    right: 15%;
    animation-delay: -5s;
}

.floating-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: -10s;
}

.floating-4 {
    top: 40%;
    right: 25%;
    animation-delay: -7s;
}

/* ===================================================================
   RESPONSIVE DESIGN ENHANCEMENTS
   ================================================================= */

/* Mobile Optimizations */
@media (max-width: 768px) {
    .hero-section {
        min-height: 100vh;
        padding: var(--space-lg) 0;
    }

    .hero-badge-enhanced {
        margin-bottom: var(--space-xl);
    }

    .badge-content {
        padding: var(--space-sm) var(--space-lg);
    }

    .badge-text {
        font-size: 0.85rem;
        letter-spacing: 1px;
    }

    .title-line-1 {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .title-line-3 {
        font-size: clamp(2.5rem, 10vw, 4rem);
    }

    .hero-subtitle-enhanced {
        font-size: clamp(1rem, 4vw, 1.3rem);
        line-height: 1.6;
    }

    .stats-container {
        flex-direction: column;
        gap: var(--space-lg);
    }

    .stat-item-enhanced {
        min-width: 160px;
        padding: var(--space-lg);
    }

    .stat-divider {
        display: none;
    }

    .hero-buttons-enhanced {
        flex-direction: column;
        gap: var(--space-lg);
        align-items: center;
    }

    .hero-button-premium {
        min-width: 280px;
        padding: var(--space-lg) var(--space-xl);
    }

    .trust-logos-enhanced {
        flex-direction: column;
        gap: var(--space-md);
        align-items: center;
    }

    .trust-logo-enhanced {
        min-width: 200px;
    }

    .floating-elements {
        display: none; /* Hide floating elements on mobile for performance */
    }

    .hero-particles .particle {
        display: none; /* Reduce particles on mobile */
    }

    .hero-particles .particle:nth-child(-n+3) {
        display: block; /* Show only first 3 particles */
    }
}

/* Tablet Optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
    .stats-container {
        gap: var(--space-lg);
    }

    .stat-item-enhanced {
        min-width: 160px;
    }

    .hero-buttons-enhanced {
        gap: var(--space-lg);
    }

    .trust-logos-enhanced {
        gap: var(--space-md);
    }
}

/* Large Screen Optimizations */
@media (min-width: 1400px) {
    .hero-section {
        min-height: 110vh;
    }

    .container {
        max-width: 1400px;
    }

    .title-line-1 {
        font-size: clamp(3rem, 4vw, 4.5rem);
    }

    .title-line-3 {
        font-size: clamp(4rem, 5vw, 6rem);
    }

    .hero-subtitle-enhanced {
        font-size: clamp(1.3rem, 2vw, 1.8rem);
    }

    .stat-item-enhanced {
        min-width: 200px;
        padding: var(--space-2xl);
    }

    .hero-button-premium {
        min-width: 250px;
        padding: var(--space-xl) var(--space-3xl);
        font-size: 1.2rem;
    }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-section {
        background-attachment: fixed;
    }

    .badge-glow,
    .stat-glow,
    .logo-glow,
    .button-border-glow {
        filter: blur(10px);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #0f172a;
        --gray-100: #1e293b;
        --gray-200: #334155;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .hero-particles,
    .floating-elements,
    .hero-shapes {
        display: none;
    }
}

/* Print Styles */
@media print {
    .hero-section {
        background: var(--white) !important;
        color: var(--gray-900) !important;
        min-height: auto;
        page-break-inside: avoid;
    }

    .hero-particles,
    .floating-elements,
    .hero-shapes,
    .scroll-indicator-enhanced {
        display: none !important;
    }

    .hero-button-premium {
        border: 2px solid var(--gray-900) !important;
        background: transparent !important;
        color: var(--gray-900) !important;
    }
}

/* ===================================================================
   KEYFRAME ANIMATIONS
   ================================================================= */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes floatShape {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(80px, 80px) rotate(90deg);
    }
    50% {
        transform: translate(-60px, 120px) rotate(180deg);
    }
    75% {
        transform: translate(-100px, -80px) rotate(270deg);
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes patternMove {
    0% {
        transform: translateX(0) translateY(0);
    }
    100% {
        transform: translateX(50px) translateY(50px);
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes expandWidth {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes floatIcon {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
    }
    75% {
        transform: translateY(-30px) rotate(270deg);
    }
}

@keyframes scroll {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(0);
    }
    50% {
        opacity: 1;
        transform: translateX(-50%) translateY(15px);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(30px);
    }
}

@keyframes arrowDown {
    0% {
        opacity: 0;
        transform: rotate(45deg) translateY(-10px);
    }
    50% {
        opacity: 1;
        transform: rotate(45deg) translateY(0);
    }
    100% {
        opacity: 0;
        transform: rotate(45deg) translateY(10px);
    }
}
    50% {
        transform: translate(0, 160px) rotate(180deg);
    }
    75% {
        transform: translate(-80px, 80px) rotate(270deg);
    }
}

@keyframes gradientFlow {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes gradientText {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes shine {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes scroll {
    0% {
        transform: translate(-50%, 0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, 25px);
        opacity: 0;
    }
}

@keyframes arrowDown {
    0% {
        transform: rotate(45deg) translate(-20px, -20px);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: rotate(45deg) translate(20px, 20px);
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* ===================================================================
   SERVICE CARDS
   ================================================================= */

.service-card {
    position: relative;
    transition: all var(--transition-bounce);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-crimson);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 0;
    border-radius: inherit;
}

.service-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transform: rotate(45deg);
    transition: all var(--transition-slow);
    opacity: 0;
}

.service-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: var(--shadow-2xl);
    border-color: var(--crimson);
}

.service-card:hover::before {
    opacity: 0.08;
}

.service-card:hover::after {
    opacity: 1;
    transform: rotate(45deg) translate(50%, 50%);
}

.service-card .icon-container {
    position: relative;
    z-index: 2;
    background: var(--gradient-royal);
    animation: float 4s ease-in-out infinite;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-royal);
}

.service-card:hover .icon-container {
    background: var(--gradient-crimson);
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-glow);
}

.service-card h3 {
    position: relative;
    z-index: 2;
    transition: color var(--transition-normal);
}

.service-card:hover h3 {
    color: var(--crimson);
}

.service-card p {
    position: relative;
    z-index: 2;
}

.service-card a {
    position: relative;
    z-index: 2;
    transition: all var(--transition-normal);
}

.service-card:hover a {
    color: var(--crimson);
    transform: translateX(5px);
}

/* ===================================================================
   ABOUT SECTION
   ================================================================= */
.about-image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-xl);
}

.about-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-crimson);
    opacity: 0.1;
    z-index: 1;
    transition: opacity var(--transition-normal);
    border-radius: inherit;
}

.about-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px solid var(--crimson);
    border-radius: inherit;
    transform: translate(20px, 20px);
    z-index: -1;
    transition: transform var(--transition-normal);
}

.about-image-container:hover::before {
    opacity: 0.2;
}

.about-image-container:hover::after {
    transform: translate(25px, 25px);
    border-color: var(--gold);
}

.about-image-container .w-full {
    transition: transform var(--transition-normal);
}

.about-image-container:hover .w-full {
    transform: scale(1.05);
}

/* ===================================================================
   PRODUCT CARDS
   ================================================================= */

.product-card {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-bounce);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: left var(--transition-slow);
    z-index: 1;
}

.product-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-crimson);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-normal);
}

.product-card:hover::before {
    left: 100%;
}

.product-card:hover::after {
    transform: scaleX(1);
}

.product-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

.product-image {
    transition: transform var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.product-card .p-8 {
    position: relative;
    z-index: 2;
}

.product-card h3 {
    transition: color var(--transition-normal);
}

.product-card:hover h3 {
    color: var(--crimson);
}

.product-card button {
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.product-card button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-crimson);
    transition: left var(--transition-normal);
    z-index: -1;
}

.product-card button:hover::before {
    left: 0;
}

.product-card button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

/* ===================================================================
   TESTIMONIAL CARDS
   ================================================================= */
.testimonial-card {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-bounce);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -30px;
    left: 20px;
    font-size: 140px;
    background: var(--gradient-crimson);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    opacity: 0.08;
    font-family: var(--font-playfair);
    font-weight: 700;
    z-index: 1;
}

.testimonial-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-royal);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 0;
}

.testimonial-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-2xl);
    border-color: var(--crimson);
}

.testimonial-card:hover::after {
    opacity: 0.03;
}

.testimonial-card .flex {
    position: relative;
    z-index: 2;
}

.testimonial-card p {
    position: relative;
    z-index: 2;
}

.testimonial-card .flex.text-yellow-400 {
    position: relative;
    z-index: 2;
}

/* ===================================================================
   CONTACT FORM
   ================================================================= */
.contact-form input,
.contact-form textarea {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all var(--transition-normal);
    border-radius: var(--radius-lg);
    font-family: var(--font-montserrat);
    font-weight: 500;
}

.contact-form input:focus,
.contact-form textarea:focus {
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
    border-color: var(--crimson);
    outline: none;
    background: rgba(255, 255, 255, 1);
}

.contact-form label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--space-sm);
    display: block;
}

.contact-form button {
    position: relative;
    overflow: hidden;
    background: var(--gradient-crimson);
    background-size: 200% 200%;
    transition: all var(--transition-bounce);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: none;
    cursor: pointer;
}

.contact-form button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.contact-form button:hover::before {
    left: 100%;
}

.contact-form button:hover {
    background-position: right center;
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-glow), var(--shadow-2xl);
}

/* ===================================================================
   SOCIAL ICONS
   ================================================================= */
.social-icon {
    position: relative;
    transition: all var(--transition-bounce);
    background: var(--gradient-royal);
    background-size: 200% 200%;
    overflow: hidden;
    border-radius: var(--radius-full);
}

.social-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-crimson);
    opacity: 0;
    transition: opacity var(--transition-normal);
    border-radius: inherit;
}

.social-icon:hover::before {
    opacity: 1;
}

.social-icon:hover {
    transform: translateY(-5px) rotate(10deg) scale(1.1);
    box-shadow: var(--shadow-glow);
}

.social-icon i {
    position: relative;
    z-index: 2;
    transition: transform var(--transition-normal);
}

.social-icon:hover i {
    transform: scale(1.2);
}

/* ===================================================================
   MOBILE MENU
   ================================================================= */
.mobile-menu {
    background: var(--gradient-royal);
    backdrop-filter: blur(15px);
    transform: translateX(-100%);
    transition: transform var(--transition-bounce);
    box-shadow: var(--shadow-2xl);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu.active {
    transform: translateX(0);
}

.mobile-menu nav a {
    position: relative;
    transition: all var(--transition-normal);
    padding: var(--space-lg);
    border-radius: var(--radius-md);
    margin: var(--space-xs) 0;
    display: block;
}

.mobile-menu nav a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-crimson);
    transform: scaleY(0);
    transition: transform var(--transition-normal);
}

.mobile-menu nav a:hover::before {
    transform: scaleY(1);
}

.mobile-menu nav a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(10px);
}

/* ===================================================================
   SCROLL PROGRESS BAR
   ================================================================= */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-crimson);
    transform-origin: 0 50%;
    transform: scaleX(0);
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(215, 38, 61, 0.3);
}

/* ===================================================================
   RESPONSIVE DESIGN
   ================================================================= */

/* Large Tablets and Small Desktops */
@media (max-width: 1024px) {
    .hero-content h2 {
        font-size: clamp(2rem, 5vw, 3.5rem);
    }

    .hero-stats {
        gap: var(--space-2xl);
    }

    .trust-logos {
        gap: var(--space-lg);
    }

    .shape-1,
    .shape-2,
    .shape-3 {
        opacity: 0.05;
    }
}

/* Tablets */
@media (max-width: 768px) {
    :root {
        --space-3xl: 2.5rem;
        --space-2xl: 2rem;
    }

    .hero-section {
        min-height: 90vh;
        padding: var(--space-2xl) 0;
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--space-lg);
        align-items: center;
    }

    .stat-item {
        align-items: center;
        min-width: auto;
        width: 100%;
        max-width: 200px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--space-md);
    }

    .hero-button {
        width: 100%;
        max-width: 280px;
        min-width: auto;
    }

    .trust-logos {
        flex-direction: column;
        align-items: center;
        gap: var(--space-md);
    }

    .shape {
        display: none;
    }

    .service-card,
    .product-card {
        margin-bottom: var(--space-lg);
    }

    .about-image-container::after {
        transform: translate(15px, 15px);
    }

    .about-image-container:hover::after {
        transform: translate(20px, 20px);
    }
}

/* Mobile Phones */
@media (max-width: 480px) {
    :root {
        --space-3xl: 2rem;
        --space-2xl: 1.5rem;
        --space-xl: 1.25rem;
    }

    .hero-section {
        min-height: 85vh;
        padding: var(--space-xl) 0;
    }

    .hero-content h2 {
        font-size: clamp(1.8rem, 8vw, 2.5rem);
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: var(--space-2xl);
    }

    .hero-badge {
        padding: var(--space-xs) var(--space-md);
        margin-bottom: var(--space-xl);
    }

    .badge-text {
        font-size: 0.8rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .hero-button {
        padding: var(--space-md) var(--space-xl);
        font-size: 1rem;
        max-width: 250px;
    }

    .trust-logo {
        font-size: 0.8rem;
        padding: var(--space-xs) var(--space-md);
    }

    .scroll-indicator {
        bottom: var(--space-lg);
    }

    .mouse {
        width: 28px;
        height: 45px;
    }

    .service-card:hover,
    .product-card:hover,
    .testimonial-card:hover {
        transform: translateY(-8px) scale(1.01);
    }

    .contact-form input,
    .contact-form textarea {
        padding: var(--space-md);
    }

    .contact-form button {
        padding: var(--space-md) var(--space-xl);
    }
}

/* Extra Small Devices */
@media (max-width: 320px) {
    .hero-content h2 {
        font-size: 1.6rem;
    }

    .hero-button {
        max-width: 220px;
        padding: var(--space-sm) var(--space-lg);
    }

    .stat-number {
        font-size: 1.8rem;
    }
}

/* ===================================================================
   PRINT STYLES
   ================================================================= */
@media print {
    .hero-shapes,
    .scroll-progress,
    .scroll-indicator,
    .mobile-menu {
        display: none !important;
    }

    .hero-section {
        background: var(--white) !important;
        color: var(--gray-900) !important;
        min-height: auto !important;
    }

    .service-card,
    .product-card,
    .testimonial-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid var(--gray-300) !important;
    }
}

/* ===================================================================
   ACCESSIBILITY IMPROVEMENTS
   ================================================================= */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero-shapes,
    .scroll-indicator {
        display: none;
    }
}

/* Focus styles for better accessibility */
button:focus,
a:focus,
input:focus,
textarea:focus {
    outline: 2px solid var(--crimson);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --royal-blue: #000080;
        --crimson: #dc143c;
        --gold: #ffd700;
    }
}