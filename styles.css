/* Creative and Innovative Styles for Creative Hydrolics */

/* Custom Animations */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes shine {
    0% { background-position: -100% 0; }
    100% { background-position: 200% 0; }
}

/* Global Styles */
:root {
    --royal-blue: #0A2463;
    --crimson: #D7263D;
    --gold: #DAA520;
    --light-blue: #B6D6F2;
    --gradient-1: linear-gradient(135deg, var(--royal-blue), #1a365d);
    --gradient-2: linear-gradient(135deg, var(--crimson), #ff4d6d);
}

body {
    scroll-behavior: smooth;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Header */
header {
    backdrop-filter: blur(10px);
    background: rgba(10, 36, 99, 0.95) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

header nav a {
    position: relative;
    padding: 0.5rem 0;
    overflow: hidden;
}

header nav a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-2);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

header nav a:hover::before {
    transform: translateX(0);
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    background: var(--gradient-1);
    background-size: 400% 400%;
    animation: gradientFlow 15s ease infinite;
    overflow: hidden;
}

/* Animated Background Elements */
.hero-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.1;
    animation: floatShape 20s infinite;
}

.shape-1 {
    width: 400px;
    height: 400px;
    background: var(--crimson);
    top: -100px;
    right: -100px;
    animation-delay: 0s;
}

.shape-2 {
    width: 300px;
    height: 300px;
    background: var(--royal-blue);
    bottom: -50px;
    left: -50px;
    animation-delay: -5s;
}

.shape-3 {
    width: 200px;
    height: 200px;
    background: var(--gold);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: -10s;
}

/* Hero Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInDown 0.8s ease-out;
}

.badge-icon {
    color: var(--gold);
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.badge-text {
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Hero Content */
.hero-content {
    position: relative;
    z-index: 3;
    padding-top: 2rem;
}

.hero-content h2 {
    font-size: clamp(2.5rem, 5vw, 4.5rem);
    line-height: 1.2;
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease-out;
}

.hero-content h2 span {
    background-size: 200% auto;
    animation: gradientText 8s linear infinite;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.5rem);
    max-width: 700px;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 0.2s forwards;
    opacity: 0;
}

/* Stats Section */
.hero-stats {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 0.4s forwards;
    opacity: 0;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--crimson);
    line-height: 1;
    margin-bottom: 0.5rem;
    font-family: 'Playfair Display', serif;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Enhanced Buttons */
.hero-buttons {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 0.6s forwards;
    opacity: 0;
}

.hero-button {
    position: relative;
    padding: 1.2rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.button-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transform: skewX(-25deg);
    transition: 0.5s;
}

.hero-button:hover .button-shine {
    left: 150%;
}

/* Trust Indicators */
.hero-trust {
    animation: fadeInUp 1s ease-out 0.8s forwards;
    opacity: 0;
}

.trust-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 1rem;
}

.trust-logos {
    display: flex;
    gap: 2rem;
}

.trust-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.trust-logo i {
    color: var(--gold);
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    animation: fadeIn 1s ease-out 1s forwards;
    opacity: 0;
}

.mouse {
    width: 30px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    position: relative;
    margin: 0 auto 10px;
}

.wheel {
    width: 4px;
    height: 8px;
    background: white;
    border-radius: 2px;
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    animation: scroll 2s infinite;
}

.arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.arrow span {
    width: 8px;
    height: 8px;
    border-right: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(45deg);
    animation: arrowDown 2s infinite;
    opacity: 0;
}

.arrow span:nth-child(2) { animation-delay: 0.2s; }
.arrow span:nth-child(3) { animation-delay: 0.4s; }

/* Animations */
@keyframes floatShape {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(50px, 50px) rotate(90deg);
    }
    50% {
        transform: translate(0, 100px) rotate(180deg);
    }
    75% {
        transform: translate(-50px, 50px) rotate(270deg);
    }
}

@keyframes gradientText {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes scroll {
    0% { transform: translate(-50%, 0); opacity: 1; }
    100% { transform: translate(-50%, 20px); opacity: 0; }
}

@keyframes arrowDown {
    0% { transform: rotate(45deg) translate(-20px, -20px); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: rotate(45deg) translate(20px, 20px); opacity: 0; }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        min-height: 90vh;
        text-align: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
    }

    .stat-item {
        align-items: center;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-button {
        width: 100%;
        max-width: 300px;
    }

    .trust-logos {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .shape {
        display: none;
    }
}

/* Service Cards */
.service-card {
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-2);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 0;
    border-radius: inherit;
}

.service-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-card:hover::before {
    opacity: 0.05;
}

.service-card .icon-container {
    position: relative;
    z-index: 1;
    background: var(--gradient-1);
    animation: float 3s ease-in-out infinite;
}

/* About Section */
.about-image-container {
    position: relative;
    overflow: hidden;
}

.about-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-2);
    opacity: 0.1;
    z-index: 1;
    transition: opacity 0.4s ease;
}

.about-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px solid var(--crimson);
    transform: translate(15px, 15px);
    z-index: -1;
    transition: transform 0.4s ease;
}

.about-image-container:hover::before {
    opacity: 0.2;
}

.about-image-container:hover::after {
    transform: translate(20px, 20px);
}

/* Product Cards */
.product-card {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: 0.5s;
}

.product-card:hover::before {
    left: 100%;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.product-image {
    transition: transform 0.4s ease;
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

/* Testimonial Card */
.testimonial-card {
    position: relative;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -20px;
    left: 20px;
    font-size: 120px;
    background: var(--gradient-2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    opacity: 0.1;
    font-family: 'Playfair Display', serif;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Contact Form */
.contact-form input,
.contact-form textarea {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(215, 38, 61, 0.1);
    border-color: var(--crimson);
    outline: none;
}

.contact-form button {
    position: relative;
    overflow: hidden;
    background: var(--gradient-2);
    background-size: 200% 200%;
    transition: all 0.4s ease;
}

.contact-form button:hover {
    background-position: right center;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(215, 38, 61, 0.2);
}

/* Social Icons */
.social-icon {
    position: relative;
    transition: all 0.3s ease;
    background: var(--gradient-1);
    background-size: 200% 200%;
}

.social-icon:hover {
    transform: translateY(-3px) rotate(8deg);
    background-position: right center;
}

/* Mobile Menu */
.mobile-menu {
    background: var(--gradient-1);
    backdrop-filter: blur(10px);
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.active {
    transform: translateX(0);
}

/* Custom Selection */
::selection {
    background: var(--crimson);
    color: white;
}

/* Scroll Progress Bar */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-2);
    transform-origin: 0 50%;
    transform: scaleX(0);
    z-index: 1000;
} 