// ===================================================================
// CREATIVE HYDROLICS - HERO SECTION ANIMATIONS
// Premium Interactive Effects and Animations
// ===================================================================

document.addEventListener('DOMContentLoaded', function() {
    
    // ===================================================================
    // ANIMATED COUNTER FUNCTION
    // ===================================================================
    function animateCounter(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }

    // ===================================================================
    // INTERSECTION OBSERVER FOR ANIMATIONS
    // ===================================================================
    const observerOptions = {
        threshold: 0.3,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Animate counters when stats section comes into view
                if (entry.target.classList.contains('hero-stats-enhanced')) {
                    const counters = entry.target.querySelectorAll('.stat-number');
                    counters.forEach(counter => {
                        const target = parseInt(counter.getAttribute('data-count'));
                        animateCounter(counter, target, 2500);
                    });
                }
            }
        });
    }, observerOptions);

    // Observe stats section
    const statsSection = document.querySelector('.hero-stats-enhanced');
    if (statsSection) {
        observer.observe(statsSection);
    }

    // ===================================================================
    // BUTTON RIPPLE EFFECTS
    // ===================================================================
    const buttons = document.querySelectorAll('.hero-button-premium');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = this.querySelector('.button-ripple');
            if (ripple) {
                ripple.style.width = '0';
                ripple.style.height = '0';
                
                setTimeout(() => {
                    ripple.style.width = '300px';
                    ripple.style.height = '300px';
                }, 10);
                
                setTimeout(() => {
                    ripple.style.width = '0';
                    ripple.style.height = '0';
                }, 600);
            }
        });
    });

    // ===================================================================
    // SCROLL INDICATOR FUNCTIONALITY
    // ===================================================================
    const scrollIndicator = document.querySelector('.scroll-indicator-enhanced');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const servicesSection = document.querySelector('#services');
            if (servicesSection) {
                servicesSection.scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }

    // ===================================================================
    // PARALLAX EFFECT FOR FLOATING ELEMENTS
    // ===================================================================
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.floating-icon');
        
        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });

        // Parallax for background shapes
        const shapes = document.querySelectorAll('.shape');
        shapes.forEach((shape, index) => {
            const speed = 0.2 + (index * 0.05);
            const yPos = -(scrolled * speed);
            shape.style.transform = `translateY(${yPos}px)`;
        });
    });

    // ===================================================================
    // TYPEWRITER EFFECT FOR SUBTITLE
    // ===================================================================
    function typewriterEffect(element, text, speed = 50) {
        element.innerHTML = '';
        let i = 0;
        
        function typeChar() {
            if (i < text.length) {
                if (text.charAt(i) === '<') {
                    // Handle HTML tags
                    const tagEnd = text.indexOf('>', i);
                    element.innerHTML += text.substring(i, tagEnd + 1);
                    i = tagEnd + 1;
                } else {
                    element.innerHTML += text.charAt(i);
                    i++;
                }
                setTimeout(typeChar, speed);
            }
        }
        
        typeChar();
    }

    // Apply typewriter effect to subtitle after a delay
    setTimeout(() => {
        const subtitle = document.querySelector('.hero-subtitle-enhanced');
        if (subtitle) {
            const originalText = subtitle.innerHTML;
            typewriterEffect(subtitle, originalText, 30);
        }
    }, 2000);

    // ===================================================================
    // DYNAMIC PARTICLE GENERATION
    // ===================================================================
    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 10 + 15) + 's';
        particle.style.animationDelay = Math.random() * 5 + 's';
        
        const particlesContainer = document.querySelector('.hero-particles');
        if (particlesContainer) {
            particlesContainer.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 25000);
        }
    }

    // Generate particles periodically
    setInterval(createParticle, 3000);

    // ===================================================================
    // MOUSE MOVEMENT PARALLAX
    // ===================================================================
    document.addEventListener('mousemove', function(e) {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;
        
        // Move floating elements based on mouse position
        const floatingElements = document.querySelectorAll('.floating-icon');
        floatingElements.forEach((element, index) => {
            const speed = (index + 1) * 0.5;
            const x = (mouseX - 0.5) * speed * 20;
            const y = (mouseY - 0.5) * speed * 20;
            
            element.style.transform += ` translate(${x}px, ${y}px)`;
        });

        // Subtle parallax for hero shapes
        const shapes = document.querySelectorAll('.shape');
        shapes.forEach((shape, index) => {
            const speed = (index + 1) * 0.2;
            const x = (mouseX - 0.5) * speed * 10;
            const y = (mouseY - 0.5) * speed * 10;
            
            shape.style.transform += ` translate(${x}px, ${y}px)`;
        });
    });

    // ===================================================================
    // SMOOTH REVEAL ANIMATIONS
    // ===================================================================
    const revealElements = document.querySelectorAll('[class*="animate-"]');
    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, { threshold: 0.1 });

    revealElements.forEach(element => {
        element.style.animationPlayState = 'paused';
        revealObserver.observe(element);
    });

    // ===================================================================
    // PERFORMANCE OPTIMIZATION
    // ===================================================================
    let ticking = false;
    
    function updateAnimations() {
        // Batch DOM updates for better performance
        requestAnimationFrame(() => {
            // Update any continuous animations here
            ticking = false;
        });
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateAnimations);
            ticking = true;
        }
    });

    // ===================================================================
    // ACCESSIBILITY ENHANCEMENTS
    // ===================================================================
    
    // Respect user's motion preferences
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        // Disable animations for users who prefer reduced motion
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Add keyboard navigation for interactive elements
    const interactiveElements = document.querySelectorAll('.hero-button-premium, .scroll-indicator-enhanced');
    interactiveElements.forEach(element => {
        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });

    console.log('🚀 Creative Hydrolics Hero Animations Loaded Successfully!');
});